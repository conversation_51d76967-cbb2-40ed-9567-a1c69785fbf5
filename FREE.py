# import sqlite3

# db_path = 'data.db'
# conn = sqlite3.connect(db_path)
# cur = conn.cursor()

# cur.execute("DROP TABLE IF EXISTS 'GET_Work_Order_COMP'")

# conn.commit()
# conn.close()

# import sqlite3

# db_path = "data.db"

# row = (
#     "GM17773589",     # wogroup
#     "BECAB1246",      # itemnum
#     123,              # positivequantity
#     "test",           # title
#     "<EMAIL>",  # enterby
#     321               # curbal
# )

# with sqlite3.connect(db_path) as conn:
#     cursor = conn.cursor()
#     cursor.execute(
#         """
#         INSERT INTO WO_materials (wogroup, itemnum, positivequantity, title, enterby, curbal)
#         VALUES (?, ?, ?, ?, ?, ?)
#         """,
#         row
#     )
#     conn.commit()


# import sqlite3

# db_path = "data.db"

# row = (
#     "GM16232793",     # wogroup
#     "TESTETSLKJSDFLKJSDF",      # itemnum
#     "HEPA MERV 17 needed",              # positivequantity
#     "test",           # title
#     "NG-Not Done",  # enterby
#     "1109628799"               # curbal
# )

# with sqlite3.connect(db_path) as conn:
#     cursor = conn.cursor()
#     cursor.execute(
#         """
#         INSERT INTO WO_tasks (wogroup, zwocomments, zdetail, title, zopresult, identifier)
#         VALUES (?, ?, ?, ?, ?, ?)
#         """,
#         row
#     )
#     conn.commit()

import sqlite3

db_path = "data.db"

row = (
    "GM19999999",     # wogroup
    "HVAC",      # itemnum
    "TEST",
    "WO Complete",              # positivequantity
    "2025-11-09 10:25:26+00:00",
    "Michael Powers",
    "U1UAHU00401-008",           # title
    "U1UAHU004",  # enterby
    "100084122"               # curbal
)

with sqlite3.connect(db_path) as conn:
    cursor = conn.cursor()
    cursor.execute(
        """
        INSERT INTO latest_WO_COMP (wogroup, ownergroup, title, statusdesc, changedate, changebyname, assetnum, zancestor, identifier)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
        row
    )
    conn.commit()