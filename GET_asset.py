import requests
import json
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZLGAPIASSET"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

where_clause = 'siteid="UTIL.GM" and status="ACTIVE" and zownerdepartment!="GM.UT.U"'


oslc_select = (
    "assetnum, description, zlevel, zownerdepartment.description--zownerdepartment"
)
# oslc_select = "*"

params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select,
    # "oslc.pageSize": "10"
}

# Prepare the request to see the full URL
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

print("Full URL:")
print(prepared.url)

session = requests.Session()
response = session.send(prepared, verify=False)

print("\nResponse status code:", response.status_code)
data = response.json()

import sqlite3

# --- Save to SQLite ---
conn = sqlite3.connect("data.db")
cur = conn.cursor()

# Create asset table if not exists
cur.execute("""
CREATE TABLE IF NOT EXISTS GET_asset (
    zownerdepartment TEXT,
    description TEXT,
    assetnum TEXT PRIMARY KEY,
    zlevel TEXT     
)
""")

# Extract records (Maximo can use either "member" or "rdfs:member")
records = data.get("member") or data.get("rdfs:member") or []

for rec in records:
    # Normalize into flat row
    zownerdepartment = rec.get("zownerdepartment")
    description = rec.get("description")
    assetnum = rec.get("assetnum")
    zlevel = rec.get("zlevel")

    cur.execute("""
        INSERT OR REPLACE INTO GET_asset
        (zownerdepartment, description, assetnum, zlevel)
        VALUES (?, ?, ?, ?)
    """, (zownerdepartment, description, assetnum, zlevel))

conn.commit()
conn.close()

import sqlite3

db_path = "data.db"

with sqlite3.connect(db_path) as conn:
    cursor = conn.cursor()

    # Get all columns in GET_asset
    cursor.execute("PRAGMA table_info(GET_asset)")
    columns = [row[1] for row in cursor.fetchall()]

    for col in columns:
        try:
            # 1️⃣ Remove " \u200e" (space + invisible char)
            cursor.execute(f"""
                UPDATE GET_asset
                SET "{col}" = REPLACE("{col}", ' ' || CHAR(8206), '')
                WHERE "{col}" LIKE '%' || ' ' || CHAR(8206) || '%';
            """)

            # 2️⃣ Remove "\u200e " (invisible char + space)
            cursor.execute(f"""
                UPDATE GET_asset
                SET "{col}" = REPLACE("{col}", CHAR(8206) || ' ', '')
                WHERE "{col}" LIKE '%' || CHAR(8206) || ' %';
            """)

            # 3️⃣ Remove any remaining standalone \u200e
            cursor.execute(f"""
                UPDATE GET_asset
                SET "{col}" = REPLACE("{col}", CHAR(8206), '')
                WHERE "{col}" LIKE '%' || CHAR(8206) || '%';
            """)

            # 4️⃣ Trim leading/trailing spaces (SQLite 3.30+ supports TRIM)
            cursor.execute(f"""
                UPDATE GET_asset
                SET "{col}" = TRIM("{col}")
                WHERE "{col}" LIKE ' %' OR "{col}" LIKE '% ';
            """)

        except Exception as e:
            print(f"⚠️ Skipped column '{col}': {e}")

    conn.commit()

print("✅ All ' \\u200e' characters and extra spaces removed successfully from GET_asset.")


print(f"Inserted {len(records)} records into GET_asset table.")