import requests
import json
import sqlite3
from datetime import datetime, timedelta, timezone
import urllib3
from utils import ShiftUtils

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

db_path = 'data.db'
conn = sqlite3.connect(db_path)
cur = conn.cursor()

# cur.execute("DROP TABLE IF EXISTS 'Daily_Check'")

conn.commit()
conn.close()
# --- Initialize ShiftUtils and get shift times ---
shift_utils = ShiftUtils()
current_time = datetime.now(shift_utils.eastern) - timedelta(hours=72)
shift_start, shift_end = shift_utils.get_shift_start_end(current_time)

# --- Convert shift times to UTC ISO strings ---
shift_start_utc = shift_start.astimezone(timezone.utc)
shift_end_utc = shift_end.astimezone(timezone.utc)
shift_start_str = shift_start_utc.isoformat()
shift_end_str = shift_end_utc.isoformat()

# --- API & DB config ---
BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZMXAPIMEASUREMENT"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

oslc_select = (
    "z_ex_measurementid, "
    "zownergroup, "
    "zassetnum, "
    "zancestor, "
    "zmeasurementvalue, "
    "zinspector, "
    "zpointnum, "
    "zobservation, "
    "zschedstart, "
    "zmeasuredate, "
    "zpmnum, "
    "zremarks"
)

# --- Function to fetch records from Maximo ---
def fetch_records(where_clause):
    params = {
        "oslc.where": where_clause,
        "lean": "1",
        "oslc.select": oslc_select
    }
    req = requests.Request(
        method="GET",
        url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
        headers=headers,
        params=params
    )
    prepared = req.prepare()
    session = requests.Session()
    response = session.send(prepared, verify=False)
    response.raise_for_status()  # Raise an exception if status != 200
    data = response.json()
    return data.get("member") or data.get("rdfs:member") or []

# --- Connect to SQLite ---
conn = sqlite3.connect("data.db")
cur = conn.cursor()

# Create table if not exists
cur.execute("""
CREATE TABLE IF NOT EXISTS Daily_Check (
    z_ex_measurementid REAL PRIMARY KEY,
    zownergroup TEXT,
    zassetnum TEXT,
    zancestor TEXT,
    zmeasurementvalue TEXT,
    zinspector TEXT,
    zpointnum REAL,
    zobservation TEXT,
    zschedstart TEXT,
    zmeasuredate TEXT, 
    zpmnum TEXT,
    zremarks TEXT
)
""")

# --- 1️⃣ Fetch using zschedstart ---
where_clause_sched = (
    f'siteid="UTIL.GM" and zschedstart>="{shift_start_str}" and zschedstart<"{shift_end_str}"'
)
records_sched = fetch_records(where_clause_sched)
print(f"Fetched {len(records_sched)} records by zschedstart")

# --- 2️⃣ Fetch using zmeasuredate ---
where_clause_measure = (
    f'siteid="UTIL.GM" and zmeasuredate>="{shift_start_str}" and zmeasuredate<"{shift_end_str}"'
)
records_measure = fetch_records(where_clause_measure)
print(f"Fetched {len(records_measure)} records by zmeasuredate")

# --- 3️⃣ Combine records ---
all_records = records_sched + records_measure
print(f"Total records to insert: {len(all_records)}")

# --- 4️⃣ Insert or replace into SQLite ---
for rec in all_records:
    cur.execute("""
        INSERT OR REPLACE INTO Daily_Check
        (z_ex_measurementid, zownergroup, zassetnum, zancestor, zmeasurementvalue, zinspector, zpointnum, zobservation, zschedstart, zmeasuredate, zpmnum, zremarks)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        rec.get("z_ex_measurementid"),
        rec.get("zownergroup"),
        rec.get("zassetnum"),
        rec.get("zancestor"),
        rec.get("zmeasurementvalue"),
        rec.get("zinspector"),
        rec.get("zpointnum"),
        rec.get("zobservation"),
        rec.get("zschedstart"),
        rec.get("zmeasuredate"),
        rec.get("zpmnum"),
        rec.get("zremarks")
    ))

conn.commit()
conn.close()

print("✅ All records inserted successfully")

# ################### Cleaning Data ##################

with sqlite3.connect(db_path) as conn:
    cursor = conn.cursor()

    # 1️⃣ Add missing columns
    cursor.execute("PRAGMA table_info(Daily_Check)")
    existing_cols = [col[1] for col in cursor.fetchall()]

    if "Equipment Name" not in existing_cols:
        cursor.execute('ALTER TABLE Daily_Check ADD COLUMN "Equipment Name" TEXT')
    if "Ancestor Name" not in existing_cols:
        cursor.execute('ALTER TABLE Daily_Check ADD COLUMN "Ancestor Name" TEXT')
    if "Display Name" not in existing_cols:
        cursor.execute('ALTER TABLE Daily_Check ADD COLUMN "Display Name" TEXT')

    # 2️⃣ Equipment Name
    cursor.execute("""
        UPDATE Daily_Check
        SET "Equipment Name" = (
            SELECT description
            FROM GET_asset
            WHERE GET_asset.assetnum = Daily_Check.zassetnum
        )
    """)

    # 3️⃣ Ancestor Name
    cursor.execute("""
        UPDATE Daily_Check
        SET "Ancestor Name" = (
            SELECT description
            FROM GET_asset
            WHERE GET_asset.assetnum = Daily_Check.zancestor
        )
    """)

    # 4️⃣ Display Name
    cursor.execute("""
        UPDATE Daily_Check
        SET "Display Name" = (
            SELECT displayname
            FROM labor_name
            WHERE labor_name.personid = Daily_Check.zinspector
        )
    """)

    # 5️⃣ zownergroup mapping
    cursor.execute("""
        UPDATE Daily_Check
        SET "zownergroup" = CASE
            WHEN "zownergroup" = 'GM.UT.O' THEN 'Operation'
            WHEN "zownergroup" = 'GM.UT.H' THEN 'HVAC'
            WHEN "zownergroup" = 'GM.UT.B' THEN 'Boiler'
            WHEN "zownergroup" = 'GM.UT.E' THEN 'Electrical'
            WHEN "zownergroup" = 'GM.UT.M' THEN 'Mechanical'
            ELSE "zownergroup"
        END
        WHERE "zownergroup" IN ('GM.UT.O', 'GM.UT.H', 'GM.UT.B', 'GM.UT.E', 'GM.UT.M');
    """)

    # 6️⃣ Ancestor mapping
    ancestor_mapping = {
        'HOT OIL CIRCULATION PUMP': 'HOT OIL PUMP',
        'ACTIVATED CARBON TOWER': 'AC TOWER',
        'PERMANENT SAFETY EYE WASH': 'SAFETY EYE WASH',
        'PERMANENT SAFETY SHOWER': 'SAFETY SHOWER',
        'RO (MIXING 2ND FLOOR)': 'RO (MIXING)',
        'DUS': 'DUST COLLECTOR',
        'FCO': 'FUME COLLECTOR',
        'HOT WATER BOILER': 'HOT WATER',
        'HOT OIL BOILER': 'HOT OIL',
        'PORTABLE SAFETY EYE WASH': 'SAFETY EYE WASH'
    }

    for old_value, new_value in ancestor_mapping.items():
        cursor.execute("""
            UPDATE Daily_Check
            SET "Ancestor Name" = ?
            WHERE "Ancestor Name" = ?;
        """, (new_value, old_value))

    conn.commit()

print("✅ Columns added and populated successfully.")
