import requests
import pandas as pd
import sqlite3
import urllib3
from datetime import datetime, timedelta, timezone
from utils import ShiftUtils

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "oslcwodetail"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

# Initialize ShiftUtils and get shift times
shift_utils = ShiftUtils()
current_time = datetime.now(shift_utils.eastern) - timedelta(hours=12)
shift_start, shift_end = shift_utils.get_shift_start_end(current_time)

# Convert to UTC
shift_start_utc = shift_start.astimezone(timezone.utc)
shift_end_utc = shift_end.astimezone(timezone.utc)

# Convert to ISO 8601 strings with UTC offset
shift_start_str = shift_start_utc.isoformat()
shift_end_str = shift_end_utc.isoformat()

print(f"Shift start (UTC): {shift_start_str}")
print(f"Shift end   (UTC): {shift_end_str}")

# Now build the WHERE clause safely
where_clause = (
    'spi_wm:siteid="UTIL.GM" and '
    'spi_wm:ownergroup!="GM.UT.U" and '
    'spi_wm:istask=0 and '
    'spi:status!="DRAFT" and '
    'spi:status!="INPRG" and '
    'spi:status!="OPCOMP" and '
    'spi:status!="CAN" and '
    'spi_wm:haschildren=true and '
    f'spi_wm:statusdate>="{shift_start_str}" and spi_wm:statusdate<"{shift_end_str}"'
)

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

oslc_select = (
    "spi_wm:wogroup,"
    "ownergroup.description--ownergroup"
    "ownergroup.description--ownergroup,"
    "dcterms:title,"
    "rel.wostatus{statusdesc.description--statusdesc,"
    "changedate,changebyname.displayname--changebyname},"
    "spi_wm:zancestor,"
    "dcterms:identifier,"
    "spi_wm:assetdowntimereport{spi_wm:assetnum},"
    "spi_wm:task{spi_wm:zwocomments, spi_wm:zdetail, dcterms:title, spi_wm:zopresult, dcterms:identifier},"
    "spi_wm:actualmaterial{spi_wm:positivequantity, spi_wm:itemnum, dcterms:title, spi_wm:enterby, spi_wm:curbal}"
)

# oslc_select = "*"

params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select
}

DB_PATH = "data.db"


def fetch_data():
    """Fetch data from Maximo API"""
    req = requests.Request(
        method="GET",
        url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
        headers=headers,
        params=params
    )
    prepared = req.prepare()
    print("Full URL:")
    print(prepared.url)

    session = requests.Session()
    response = session.send(prepared, verify=False)

    print("\nResponse status code:", response.status_code)

    if response.status_code != 200:
        print("❌ Failed to get data:", response.text)
        return None

    return response.json()


def get_latest_WO_COMP(data):
    """Extract and save the latest WO Complete status to SQLite."""
    cols = [
        "wogroup", "ownergroup", "title", "statusdesc", "changedate",
        "changebyname", "assetnum", "zancestor", "identifier"
    ]
    if not data:
        print("⚠️ No data provided.")
        df_empty = pd.DataFrame(columns=cols)
        with sqlite3.connect(DB_PATH) as conn:
            df_empty.to_sql("latest_WO_COMP", conn, if_exists="replace", index=False)
        return df_empty

    status_records = []

    for wo in data.get("member", []):
        wogroup = wo.get("wogroup")
        ownergroup = wo.get("ownergroupownergroup.description")
        title = wo.get("title")
        zancestor = wo.get("zancestor")
        identifier = wo.get("identifier")

        assetdowntime_list = wo.get("assetdowntimereport", [])
        asset_nums = ', '.join(list(set(
            [asset.get('assetnum', '') for asset in assetdowntime_list if asset.get('assetnum')]
        )))

        for ws in wo.get("wostatus", []):
            status_records.append({
                "wogroup": wogroup,
                "ownergroup": ownergroup,
                "title": title,
                "statusdesc": ws.get("statusdesc"),
                "changedate": ws.get("changedate"),
                "changebyname": ws.get("changebyname"),
                "assetnum": asset_nums,
                "zancestor": zancestor,
                "identifier": identifier
            })

    df_status = pd.DataFrame(status_records)

    if df_status.empty:
        print("⚠️ No status records found. Saving empty table.")
        df_empty = pd.DataFrame(columns=cols)
        with sqlite3.connect(DB_PATH) as conn:
            df_empty.to_sql("latest_WO_COMP", conn, if_exists="replace", index=False)
        return df_empty

    df_status["changedate"] = pd.to_datetime(df_status["changedate"], errors="coerce")
    df_status = df_status.sort_values(by="changedate", ascending=False)
    df_wo_complete = df_status[df_status["statusdesc"] == "WO Complete"]
    df_wo_complete = df_wo_complete[(df_wo_complete['changedate'] >= shift_start_str) & (df_wo_complete['changedate'] < shift_end_str)]
    df_wo_complete['assetnum'] = df_wo_complete['assetnum'].str[:15]

    if df_wo_complete.empty:
        print("⚠️ No 'WO Complete' status found. Saving empty table.")
        df_empty = pd.DataFrame(columns=cols)
        with sqlite3.connect(DB_PATH) as conn:
            df_empty.to_sql("latest_WO_COMP", conn, if_exists="replace", index=False)
        return df_empty

    df_latest = df_wo_complete.groupby("wogroup", as_index=False).first()

    with sqlite3.connect(DB_PATH) as conn:
        df_latest.to_sql("latest_WO_COMP", conn, if_exists="replace", index=False)

    print(f"\n✅ Saved {len(df_latest)} rows to 'latest_WO_COMP' in {DB_PATH}")
    return df_latest


def get_tasks(data):
    """Extract and save task information for each wogroup to SQLite."""
    cols = [
        "wogroup", "zwocomments", "zdetail", "title",
        "zopresult", "identifier"
    ]
    if not data:
        print("⚠️ No data provided.")
        df_empty = pd.DataFrame(columns=cols)
        with sqlite3.connect(DB_PATH) as conn:
            df_empty.to_sql("WO_tasks", conn, if_exists="replace", index=False)
        return df_empty

    tasks_records = []
    for wo in data.get("member", []):
        wogroup = wo.get("wogroup")
        for task in wo.get("task", []):
            tasks_records.append({
                "wogroup": wogroup,
                "zwocomments": task.get("zwocomments"),
                "zdetail": task.get("zdetail"),
                "title": task.get("title"),
                "zopresult": task.get("zopresult"),
                "identifier": task.get("identifier")
            })

    df_tasks = pd.DataFrame(tasks_records)

    if df_tasks.empty:
        print("⚠️ No task records found. Saving empty table.")
        df_empty = pd.DataFrame(columns=cols)
        with sqlite3.connect(DB_PATH) as conn:
            df_empty.to_sql("WO_tasks", conn, if_exists="replace", index=False)
        return df_empty

    with sqlite3.connect(DB_PATH) as conn:
        df_tasks.to_sql("WO_tasks", conn, if_exists="replace", index=False)

    print(f"\n✅ Saved {len(df_tasks)} rows to 'WO_tasks' in {DB_PATH}")
    return df_tasks


def get_materials(data):
    """Extract and save material information for each wogroup to SQLite."""
    cols = [
        "wogroup", "itemnum", "positivequantity", "title",
        "enterby", "curbal"
    ]
    if not data:
        print("⚠️ No data provided.")
        df_empty = pd.DataFrame(columns=cols)
        with sqlite3.connect(DB_PATH) as conn:
            df_empty.to_sql("WO_materials", conn, if_exists="replace", index=False)
        return df_empty

    materials_records = []
    for wo in data.get("member", []):
        wogroup = wo.get("wogroup")
        for material in wo.get("actualmaterial", []):
            materials_records.append({
                "wogroup": wogroup,
                "itemnum": material.get("itemnum"),
                "positivequantity": material.get("positivequantity"),
                "title": material.get("title"),
                "enterby": material.get("enterby"),
                "curbal": material.get("curbal")
            })

    df_materials = pd.DataFrame(materials_records)

    if df_materials.empty:
        print("⚠️ No material records found. Saving empty table.")
        df_empty = pd.DataFrame(columns=cols)
        with sqlite3.connect(DB_PATH) as conn:
            df_empty.to_sql("WO_materials", conn, if_exists="replace", index=False)
        return df_empty

    with sqlite3.connect(DB_PATH) as conn:
        df_materials.to_sql("WO_materials", conn, if_exists="replace", index=False)

    print(f"\n✅ Saved {len(df_materials)} rows to 'WO_materials' in {DB_PATH}")
    return df_materials


if __name__ == "__main__":
    data = fetch_data()

    if data:
        print("\n" + "="*80)
        print("Processing Work Order Data")
        print("="*80)

        get_latest_WO_COMP(data)
        get_tasks(data)
        get_materials(data)

        print("\n✅ All data saved successfully!")
    else:
        print("❌ Failed to fetch data from API")
