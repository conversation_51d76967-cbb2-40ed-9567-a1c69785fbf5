import requests
import json
import sqlite3
from datetime import datetime, timezone
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZLGAPIZSPMST"

API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

# oslc.where must be a single string
latest_date = open('last_change_date.txt', 'r').read()
where_clause = f'zchangedate>="{latest_date}"'

# oslc.select defines which fields to return (including relationship expansion)
oslc_select = (
    "zitemnum,"
    "zspec,"
    "zmodelnum,"
    "zchangedate,"
    "z_sp_mstid"
)

# oslc_select = "*"

params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select
}

# # Prepare the request to see the full URL
# req = requests.Request(
#     method="GET",
#     url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
#     headers=headers,
#     params=params
# )
# prepared = req.prepare()

# # Print the full URL
# print("Full URL:")
# print(prepared.url)

# # Send the request
# session = requests.Session()
# response = session.send(prepared, verify=False)

# print("\nResponse status code:", response.status_code)
# data = response.json()
# # print(json.dumps(data, indent=2))

# Send request
response = requests.get(f"{BASE_URL}/oslc/os/{OBJECT_NAME}", headers=headers, params=params, verify=False)
print(response.url)
data = response.json()

# Flatten JSON to rows for SQLite
rows = []
for item in data.get('member', []):
    zitemnum = item.get('zitemnum')
    zspec = item.get('zspec')
    zmodelnum = item.get('zmodelnum')
    zchangedate = item.get('zchangedate')
    z_sp_mstid = item.get('z_sp_mstid')
    

    rows.append((zitemnum, zspec, zmodelnum, zchangedate, z_sp_mstid))

# Save to SQLite
conn = sqlite3.connect('data.db')
cursor = conn.cursor()

# Create table if not exists
cursor.execute('''
CREATE TABLE IF NOT EXISTS GET_SPARE_PART (
    zitemnum TEXT PRIMARY KEY,
    zspec TEXT,
    zmodelnum TEXT,
    zchangedate TEXT,
    z_sp_mstid TEXT
)
''')

# Insert rows
cursor.executemany('''
INSERT OR REPLACE INTO GET_SPARE_PART (zitemnum, zspec, zmodelnum, zchangedate, z_sp_mstid)
VALUES (?, ?, ?, ?, ?)
''', rows)

conn.commit()
conn.close()

print(f"Saved {len(rows)} rows into GET_SPARE_PART table.")

# Get current UTC time in the required format
current_utc = datetime.now(timezone.utc).isoformat(timespec='seconds')

# Write to file
with open('last_change_date.txt', 'w') as f:
    f.write(current_utc)

print(f"Updated last_change_date.txt to {current_utc}")
