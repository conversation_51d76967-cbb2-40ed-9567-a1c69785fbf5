import sqlite3
import pandas as pd
import openpyxl
from datetime import datetime

DB_PATH = "../data.db"  # Database is in parent directory
TEMPLATE_FILE = "FRM-ENV-006- HAZTANK LOG.xlsx"  # Template is in same directory as script

zpmnums = [
    "CHK00313995","CHK00313996","CHK00313997","CHK00313998",
    "CHK00313999","CHK00314000","CHK00314001","CHK00314002",
    "CHK00314003","CHK00314004","CHK00314005","CHK00314006"
]

# Build placeholders for SQL IN clause
placeholders = ",".join(["?"] * len(zpmnums))

query = f"""
SELECT *
FROM Daily_Check
WHERE zpmnum IN ({placeholders})
  AND zschedstart >= date('now', 'start of month')
  AND zschedstart <  date('now', 'start of month', '+1 month')
"""


conn = sqlite3.connect(DB_PATH)
df = pd.read_sql_query(query, conn, params=zpmnums)
conn.close()

# Convert zschedstart from UTC → Eastern Time
df["zschedstart"] = (
    pd.to_datetime(df["zschedstart"], utc=True)
      .dt.tz_convert("US/Eastern")
)

# Convert zmeasuredate from UTC → Eastern Time if not null
df["zmeasuredate"] = pd.to_datetime(df["zmeasuredate"], utc=True, errors='coerce').dt.tz_convert("US/Eastern")

print(f"Total records found: {len(df)}")
print(df.head())

# Group data by date (zschedstart) to create rows for the Excel table
# Each row represents one inspection date with all the point measurements
grouped_data = []

# Group by zschedstart date to get unique inspection dates
for date, group in df.groupby(df['zschedstart'].dt.date):
    row_data = {
        'date': date,
        'time': None,
        'inspector': [],
        'tank_level': None,
        'alarm_fault': None,
        'spills_leaks_30': None,
        'corrosion_40': None,
        'spills_leaks_60': None,
        'cracks_70': None,
        'spills_leaks_80': None,
        'cracks_90': None,
        'spills_leaks_100': None,
        'cracks_110': None,
        'corrective_action': []
    }

    # Process each measurement point for this date
    for _, row in group.iterrows():
        point = row['zpointnum']

        # Get time from zmeasuredate if available
        if pd.notna(row['zmeasuredate']) and row_data['time'] is None:
            row_data['time'] = row['zmeasuredate'].strftime('%H:%M')

        # Collect unique inspectors
        if pd.notna(row['Display Name']) and row['Display Name'] not in row_data['inspector']:
            row_data['inspector'].append(row['Display Name'])

        # Collect remarks for corrective action
        if pd.notna(row['zremarks']) and row['zremarks'] not in row_data['corrective_action']:
            row_data['corrective_action'].append(row['zremarks'])

        # Map measurements and observations to specific columns based on point number
        if point == 10:  # Tank Level
            row_data['tank_level'] = row['zmeasurementvalue']
        elif point == 20:  # Alarm Fault
            row_data['alarm_fault'] = row['zobservation']
        elif point == 30:  # Spills or Leaks
            row_data['spills_leaks_30'] = row['zobservation']
        elif point == 40:  # Corrosion
            row_data['corrosion_40'] = row['zobservation']
        elif point == 60:  # Spills or Leaks
            row_data['spills_leaks_60'] = row['zobservation']
        elif point == 70:  # Cracks
            row_data['cracks_70'] = row['zobservation']
        elif point == 80:  # Spills or Leaks
            row_data['spills_leaks_80'] = row['zobservation']
        elif point == 90:  # Cracks
            row_data['cracks_90'] = row['zobservation']
        elif point == 100:  # Spills or Leaks
            row_data['spills_leaks_100'] = row['zobservation']
        elif point == 110:  # Cracks
            row_data['cracks_110'] = row['zobservation']

    # Convert lists to comma-separated strings
    row_data['inspector'] = ', '.join(row_data['inspector'])
    row_data['corrective_action'] = ', '.join(row_data['corrective_action'])

    grouped_data.append(row_data)

# Sort by date (earliest to latest)
grouped_data.sort(key=lambda x: x['date'])

print(f"\nProcessed {len(grouped_data)} inspection dates")

# Load the Excel template
wb = openpyxl.load_workbook(TEMPLATE_FILE)
ws = wb.active

# Helper function to handle None, NaT, or empty values
def format_value(value):
    if value is None or pd.isna(value) or value == '' or str(value).strip() == '':
        return "-"
    return value

# Fill the table starting from row 11 (A11)
start_row = 11
for i, data in enumerate(grouped_data):
    current_row = start_row + i

    # Column A: Date
    ws[f'A{current_row}'] = format_value(data['date'])

    # Column B: Time
    ws[f'B{current_row}'] = format_value(data['time'])

    # Column C: Inspector
    ws[f'C{current_row}'] = format_value(data['inspector'])

    # Column D: Tank Level (%)
    ws[f'D{current_row}'] = format_value(data['tank_level'])

    # Column E: Alarm Fault
    ws[f'E{current_row}'] = format_value(data['alarm_fault'])

    # Column F: Spills or Leaks (point 30)
    ws[f'F{current_row}'] = format_value(data['spills_leaks_30'])

    # Column G: Corrosion (point 40)
    ws[f'G{current_row}'] = format_value(data['corrosion_40'])

    # Column H: Spills or Leaks (point 60)
    ws[f'H{current_row}'] = format_value(data['spills_leaks_60'])

    # Column I: Cracks (point 70)
    ws[f'I{current_row}'] = format_value(data['cracks_70'])

    # Column J: Spills or Leaks (point 80)
    ws[f'J{current_row}'] = format_value(data['spills_leaks_80'])

    # Column K: Cracks (point 90)
    ws[f'K{current_row}'] = format_value(data['cracks_90'])

    # Column L: Spills or Leaks (point 100)
    ws[f'L{current_row}'] = format_value(data['spills_leaks_100'])

    # Column M: Cracks (point 110)
    ws[f'M{current_row}'] = format_value(data['cracks_110'])

    # Column N: Corrective Action
    ws[f'N{current_row}'] = format_value(data['corrective_action'])

# Generate output filename with current month
current_month = datetime.now().strftime('%b').upper()  # e.g., 'JAN', 'DEC'
output_filename = f"{current_month}_FRM-ENV-006_HAZTANK LOG.xlsx"

# Save the new Excel file
wb.save(output_filename)

print(f"\n✅ Excel file created successfully: {output_filename}")
print(f"Data filled from row {start_row} to row {start_row + len(grouped_data) - 1}")

# Display summary of what was processed
print(f"\nSummary:")
print(f"- Template file: {TEMPLATE_FILE}")
print(f"- Output file: {output_filename}")
print(f"- Number of inspection dates: {len(grouped_data)}")
print(f"- Date range: {min(data['date'] for data in grouped_data)} to {max(data['date'] for data in grouped_data)}")

# Show first few rows of processed data
print(f"\nFirst few processed rows:")
for i, data in enumerate(grouped_data[:3]):
    print(f"Row {start_row + i}: {data['date']} | {data['time']} | {data['inspector'][:50]}...")