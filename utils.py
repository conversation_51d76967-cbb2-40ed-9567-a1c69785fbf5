import pytz
from datetime import datetime, timedelta
import pandas as pd
import os


class ShiftUtils:
    """
    Utility class for shift-related calculations including shift start/end times,
    shift letter determination, and file path generation.
    """
    
    def __init__(self):
        self.eastern = pytz.timezone('US/Eastern')
        self.ref_date = self.eastern.localize(datetime(1900, 1, 1))
        self.ref_date_2024 = self.eastern.localize(datetime(2024, 1, 1))
    
    def get_shift_start_end(self, current_time: datetime):
        """
        Calculate shift start and end times based on current time.
        
        Args:
            current_time (datetime): The current time to calculate shift for
            
        Returns:
            tuple: (start_time, end_time) as datetime objects
        """
        # Ensure current_time is in Eastern Time Zone
        current_time = current_time.astimezone(self.eastern)
        
        # Get the hour of the current time
        hour = current_time.hour
        
        # Round down (start datetime)
        if 6 <= hour < 18:
            # If the time is between 6 AM and 6 PM, round down to 6 AM of the same day
            start_time = current_time.replace(hour=6, minute=0, second=0, microsecond=0)
        else:
            # Otherwise, round down to 6 PM of the previous day if before 6 AM
            if hour < 6:
                start_time = (current_time - timedelta(days=1)).replace(hour=18, minute=0, second=0, microsecond=0)
            else:
                # If the time is after 6 PM, round down to 6 PM of the same day
                start_time = current_time.replace(hour=18, minute=0, second=0, microsecond=0)

        # Round up (end datetime)
        if 6 <= hour < 18:
            # If the time is between 6 AM and 6 PM, round up to 6 PM of the same day
            end_time = current_time.replace(hour=18, minute=0, second=0, microsecond=0)
        else:
            # Otherwise, round down to 6 PM of the previous day if before 6 AM
            if hour < 6:
                end_time = (current_time - timedelta(days=0)).replace(hour=6, minute=0, second=0, microsecond=0)
            else:
                # If the time is after 6 PM, round down to 6 PM of the same day
                end_time = (current_time + timedelta(days=1)).replace(hour=6, minute=0, second=0, microsecond=0)
        
        return start_time, end_time
    
    def get_shift_start_end_pandas(self, shift_start_time):
        """
        Calculate shift start and end times for pandas datetime objects.
        Used for compatibility with send_email.py
        
        Args:
            shift_start_time: pandas datetime or datetime object
            
        Returns:
            tuple: (start_time, end_time) as datetime objects
        """
        if pd.isna(shift_start_time):
            return pd.NaT, pd.NaT

        # Remove timezone info if present
        if hasattr(shift_start_time, 'tz') and shift_start_time.tz is not None:
            shift_start_time = shift_start_time.tz_localize(None)

        shift_start_time = pd.to_datetime(shift_start_time)

        if 6 <= shift_start_time.hour < 18:
            shift_start = shift_start_time.replace(hour=6, minute=0, second=0, microsecond=0)
            shift_end = shift_start_time.replace(hour=18, minute=0, second=0, microsecond=0)
        else:
            if shift_start_time.hour < 6:
                shift_start = (shift_start_time - timedelta(days=1)).replace(hour=18, minute=0, second=0, microsecond=0)
            else:
                shift_start = shift_start_time.replace(hour=18, minute=0, second=0, microsecond=0)
            shift_end = shift_start + timedelta(hours=12)

        return shift_start, shift_end
    
    def get_shift_letter(self, shift_start: datetime) -> str:
        """
        Determine the shift letter (A, B, C, or D) based on shift start time.
        
        Args:
            shift_start (datetime): The shift start time
            
        Returns:
            str: The shift letter ('A', 'B', 'C', or 'D')
        """
        # Ensure shift_start is in Eastern Time Zone
        shift_start = shift_start.astimezone(self.eastern)
        
        # Calculate days since reference date
        days_since_ref_date_shift = (shift_start - self.ref_date).days
        days_since_ref_date_2024 = (self.ref_date_2024 - self.ref_date).days

        # Shift pattern determination
        if 6 <= shift_start.hour < 18:  # Day shift: 6 AM to 6 PM
            # Day shifts (A or C)
            mod_value = (days_since_ref_date_shift - days_since_ref_date_2024 + 45292 + 18) % 6
            return "A" if mod_value < 3 else "C"
        else:
            # Night shifts (B or D)
            # For night shifts before 6 AM, use previous day's calculation
            if shift_start.hour < 6:
                days_since_ref_date_shift -= 1
            
            mod_value = (days_since_ref_date_shift - days_since_ref_date_2024 + 45292 + 18) % 6
            return "B" if mod_value < 3 else "D"
    
    def get_shift_info(self, shift_letter: str) -> str:
        """
        Get shift info (DAY/NIGHT) based on shift letter.
        
        Args:
            shift_letter (str): The shift letter ('A', 'B', 'C', or 'D')
            
        Returns:
            str: 'DAY' for A/C crews, 'NIGHT' for B/D crews
        """
        return 'DAY' if shift_letter in ['A', 'C'] else 'NIGHT'
    
    def generate_report_filename(self, start_time: datetime, shift_letter: str) -> str:
        """
        Generate a standardized report filename based on start time and shift letter.
        
        Args:
            start_time (datetime): The shift start time
            shift_letter (str): The shift letter
            
        Returns:
            str: The formatted filename
        """
        shift_info = self.get_shift_info(shift_letter)
        formatted_date = start_time.date()
        return f"{formatted_date}_{shift_info} ({shift_letter} Crew).pdf"
    
    def get_report_file_path(self, directory: str, start_time: datetime, shift_letter: str) -> str:
        """
        Generate the full file path for a report.
        
        Args:
            directory (str): The directory where the report should be saved
            start_time (datetime): The shift start time
            shift_letter (str): The shift letter
            
        Returns:
            str: The full file path
        """
        filename = self.generate_report_filename(start_time, shift_letter)
        return os.path.join(directory, filename)
