import time
import datetime
import subprocess

def run_scripts():
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"\n🕒 Running scripts at {now}")

    try:
        print("="*80)
        print("🚀 Running GET_Labor.py...")
        subprocess.run(["python", "GET_Labor.py"], check=True)
        print("✅ GET_Labor.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ GET_Labor.py failed: {e}")
        return

    try:
        print("="*80)
        print("🚀 Running Get_asset.py...")
        subprocess.run(["python", "Get_asset.py"], check=True)
        print("✅ Get_asset.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Get_asset.py failed: {e}")
        return
    
    try:
        print("="*80)
        print("🚀 Running Get_Work_Order_COMP.py...")
        subprocess.run(["python", "Get_Work_Order_COMP.py"], check=True)
        print("✅ Get_Work_Order_COMP.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Get_Work_Order_COMP.py failed: {e}")
        return
    
    try:
        print("="*80)
        print("🚀 Running GET_DocLink.py...")
        subprocess.run(["python", "GET_DocLink.py"], check=True)
        print("✅ GET_DocLink.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ GET_DocLink.py failed: {e}")
        return
    
    try:
        print("="*80)
        print("🚀 Running GET_SPARE_PART.py...")
        subprocess.run(["python", "GET_SPARE_PART.py"], check=True)
        print("✅ GET_SPARE_PART.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ GET_SPARE_PART.py failed: {e}")
        return
    
    try:
        print("="*80)
        print("🚀 Running GET_Daily_Check.py to join...")
        subprocess.run(["python", "GET_Daily_Check.py"], check=True)
        print("✅ GET_Daily_Check.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ GET_Daily_Check.py failed: {e}")
        return
    
    try:
        print("="*80)
        print("🚀 Running Create_daily_check_report.py to join...")
        subprocess.run(["python", "Create_daily_check_report.py"], check=True)
        print("✅ Create_daily_check_report.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Create_daily_check_report.py failed: {e}")
        return

    try:
        print("="*80)
        print("📧 Running send_email.py...")
        subprocess.run(["python", "send_email.py"], check=True)
        print("✅ send_email.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ send_email.py failed: {e}")

def wait_until(target_hour):
    """Wait until the next target_hour (6 or 18)."""
    now = datetime.datetime.now()
    target = now.replace(hour=target_hour, minute=0, second=0, microsecond=0)
    if now >= target:
        target += datetime.timedelta(days=1)
    wait_seconds = (target - now).total_seconds()
    print(f"⏳ Waiting until {target.strftime('%Y-%m-%d %H:%M:%S')} ({int(wait_seconds)}s)")
    time.sleep(wait_seconds)

# if __name__ == "__main__":
#     while True:
#         now = datetime.datetime.now()

#         # Determine next run: either 6am or 6pm
#         if now.hour < 6:
#             next_run_hour = 6
#         elif now.hour < 18:
#             next_run_hour = 18
#         else:
#             next_run_hour = 6  # next day's 6am

#         wait_until(next_run_hour)
#         run_scripts()

if __name__ == "__main__":
    run_scripts()