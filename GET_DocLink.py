import requests
import json
import sqlite3
import pandas as pd
from datetime import datetime, timedelta, timezone
import urllib3
from utils import ShiftUtils

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZMXAPIDOCLINKS"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

# Initialize ShiftUtils and get shift times
shift_utils = ShiftUtils()
current_time = datetime.now(shift_utils.eastern) - timedelta(hours=12)
shift_start, shift_end = shift_utils.get_shift_start_end(current_time)

# Convert to UTC
shift_start_utc = shift_start.astimezone(timezone.utc)
shift_end_utc = shift_end.astimezone(timezone.utc)

# Convert to ISO 8601 strings with UTC offset
shift_start_str = shift_start_utc.isoformat()
shift_end_str = shift_end_utc.isoformat()

print(f"Shift start (UTC): {shift_start_str}")
print(f"Shift end   (UTC): {shift_end_str}")

# oslc.where must be a single string
where_clause = (
    # 'doctype!="OPBefore" and '
    # 'doctype!="OPAfter" and '
    'doctype!="CheckMst" and '
    'doctype!="StdEqStr" and '
    'doctype!="Equipment" and '
    'doctype!="Association" and '
    # 'doctype!="Attachments" and '
    'doctype!="Activity" and '
    'doctype!="PMOperation" and '
    'doctype!="SPMST" and '
    'doctype!="PMSchedule" and '
    'doctype!="FailureCheck" and '
    'doctype!="CauseResolution" and '
    'doctype!="Inner" and '
    'doctype!="EMRequest" and '
    'doctype!="Images" and '
    'doctype!="SPMST_01" and '
    'doctype!="EBOM" and '
    'doctype!="Outer" and '
    'doctype!="EQERRCODE" and '
    'doctype!="PMWorkorder" and '
    'doctype!="BMAssociation" and '
    'doctype!="BMActivity" and '
    'doctype!="InformWO" and '
    'doctype!="Resolution" and '
    'doctype!="StockTake" and '
    'doctype!="EventWO" and '
    'doctype!="Spare" and '
    'doctype!="OPManual" and '
    'doctype!="INVPRSS" and '
    'doctype!="EventAsset" and '
    'doctype!="OPGuide" and '
    'doctype!="EventAssetUser" and '
    'doctype!="Parts" and '
    'doctype!="Delivery" and '
    'doctype!="Check" and '
    'doctype!="FdcAlarm" and '
    'doctype!="LGESWA" and '
    f'changedate>="{shift_start_str}" and changedate<"{shift_end_str}"'
)

# oslc.select defines which fields to return (including relationship expansion)
oslc_select = (
    "doctype, ownerid, changedate, createby, weburl"
)

# oslc_select = ("*")
params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select
    # "oslc.pageSize": "100"
}

# Prepare the request to see the full URL
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

# Print the full URL
print("Full URL:")
print(prepared.url)

print(f"\nselect: {oslc_select}\n")

# Send the request
session = requests.Session()
response = session.send(prepared, verify=False)

print("\nResponse status code:", response.status_code)
data = response.json()
# print(json.dumps(data, indent=2))

# Extract OSLC member data safely
records = data.get('member', [])

if not records:
    print("\nNo data returned from OSLC response.")
else:
    # Normalize to DataFrame
    df = pd.json_normalize(records)

    # Keep only required columns (if any are missing, they’ll be filled as blank)
    expected_columns = ["doctype", "ownerid", "changedate", "createby", "weburl"]
    for col in expected_columns:
        if col not in df.columns:
            df[col] = ""

    df = df[expected_columns]

    # Save to SQLite
    db_path = "data.db"
    table_name = "GET_DocLink"

    with sqlite3.connect(db_path) as conn:
        df.to_sql(table_name, conn, if_exists="replace", index=False)
        print(f"\n✅ Data successfully saved to '{table_name}' table in {db_path}")
        print(f"Total rows saved: {len(df)}")

    print("\nFirst few rows:")
    print(df.head(10))