{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   z_ex_measurementid zownergroup        zassetnum  zancestor  \\\n", "0         109056676.0  Mechanical  U1UCES00103-001  U1UCES001   \n", "1         109056707.0  Mechanical  U1UCES00103-001  U1UCES001   \n", "2         109056738.0  Mechanical  U1UCES00103-001  U1UCES001   \n", "3         109056769.0  Mechanical  U1UCES00103-001  U1UCES001   \n", "4         109056800.0  Mechanical  U1UCES00103-001  U1UCES001   \n", "\n", "  zmeasurementvalue zinspector  zpointnum zobservation  \\\n", "0              None       None       80.0         None   \n", "1              None       None       90.0         None   \n", "2              None       None      100.0         None   \n", "3              None       None       10.0         None   \n", "4              None       None       20.0         None   \n", "\n", "                zschedstart zmeasuredate       zpmnum       Equipment Name  \\\n", "0 2026-01-12 06:00:00-05:00         None  CHK00314002  CESS-WASTE TANK-101   \n", "1 2026-01-12 06:00:00-05:00         None  CHK00314003  CESS-WASTE TANK-101   \n", "2 2026-01-12 06:00:00-05:00         None  CHK00314004  CESS-WASTE TANK-101   \n", "3 2026-01-12 06:00:00-05:00         None  CHK00313995  CESS-WASTE TANK-101   \n", "4 2026-01-12 06:00:00-05:00         None  CHK00313996  CESS-WASTE TANK-101   \n", "\n", "  Ancestor Name Display Name  \n", "0          CESS         None  \n", "1          CESS         None  \n", "2          CESS         None  \n", "3          CESS         None  \n", "4          CESS         None  \n"]}], "source": ["import sqlite3\n", "import pandas as pd\n", "\n", "DB_PATH = \"data.db\"\n", "\n", "zpmnums = [\n", "    \"CHK00313995\",\"CHK00313996\",\"CHK00313997\",\"CHK00313998\",\n", "    \"CHK00313999\",\"CHK00314000\",\"CHK00314001\",\"CHK00314002\",\n", "    \"CHK00314003\",\"CHK00314004\",\"CHK00314005\",\"CHK00314006\"\n", "]\n", "\n", "# Build placeholders for SQL IN clause\n", "placeholders = \",\".join([\"?\"] * len(zpmnums))\n", "\n", "query = f\"\"\"\n", "SELECT *\n", "FROM Daily_Check\n", "WHERE zpmnum IN ({placeholders})\n", "  AND strftime('%Y-%m', zschedstart) = strftime('%Y-%m', 'now')\n", "\"\"\"\n", "\n", "conn = sqlite3.connect(DB_PATH)\n", "\n", "df = pd.read_sql_query(query, conn, params=zpmnums)\n", "\n", "conn.close()\n", "\n", "# Convert z<PERSON><PERSON><PERSON><PERSON> from UTC → Eastern Time\n", "df[\"zschedstart\"] = (\n", "    pd.to_datetime(df[\"zschedstart\"], utc=True)\n", "      .dt.tz_convert(\"US/Eastern\")\n", ")\n", "\n", "print(df.head())\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}