import sqlite3
import pandas as pd

DB_PATH = "data.db"

zpmnums = [
    "CHK00313995","CHK00313996","CHK00313997","CHK00313998",
    "CHK00313999","CHK00314000","CHK00314001","CHK00314002",
    "CHK00314003","CHK00314004","CHK00314005","CHK00314006"
]

# Build placeholders for SQL IN clause
placeholders = ",".join(["?"] * len(zpmnums))

query = f"""
SELECT *
FROM Daily_Check
WHERE zpmnum IN ({placeholders})
  AND strftime('%Y-%m', zschedstart) = strftime('%Y-%m', 'now')
"""

conn = sqlite3.connect(DB_PATH)

df = pd.read_sql_query(query, conn, params=zpmnums)

conn.close()

# Convert zschedstart from UTC → Eastern Time
df["zschedstart"] = (
    pd.to_datetime(df["zschedstart"], utc=True)
      .dt.tz_convert("US/Eastern")
)

print(df.head())