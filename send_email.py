from datetime import datetime, timedelta
import win32com.client as win32
import sqlite3
import pandas as pd
from collections import defaultdict
import os
import glob
from utils import ShiftUtils

# Initialize ShiftUtils
shift_utils = ShiftUtils()

# Example:
now = pd.Timestamp.now()
shift_start, shift_end = shift_utils.get_shift_start_end_pandas(now-timedelta(hours=12))
print("Shift start:", shift_start)
print("Shift end  :", shift_end)

conn = sqlite3.connect("data.db")
cursor = conn.cursor()

shift_start_str = shift_start.isoformat()
shift_end_str = shift_end.isoformat()

print(f"Shift start (UTC): {shift_start_str}")
print(f"Shift end   (UTC): {shift_end_str}")

cursor.execute("""
SELECT 
    w.ownergroup,
    a.description as ancestor_description,
    asset.description as asset_description,
    w.wogroup,
    w.title,
    t.zopresult as zopresult_description,
    m.itemnum,
    m.positivequantity,
    sp.zspec,
    m.curbal,
    w.changebyname,
    t.title as task_title,
    t.zwocomments,
    w.identifier,
    sp.z_sp_mstid,
    d.weburl,
    d.doctype
FROM latest_WO_COMP w
LEFT JOIN GET_asset a ON w.zancestor = a.assetnum
LEFT JOIN GET_asset asset ON w.assetnum = asset.assetnum
LEFT JOIN WO_tasks t ON w.wogroup = t.wogroup
LEFT JOIN WO_materials m ON w.wogroup = m.wogroup
LEFT JOIN GET_SPARE_PART sp ON m.itemnum = sp.zitemnum
LEFT JOIN GET_DocLink d ON w.identifier = d.ownerid
ORDER BY w.changedate DESC
""")

rows = cursor.fetchall()
conn.close()

from itertools import groupby

def build_html_table(rows):
    if not rows:
        return "<p>No records found.</p>"

    # Updated column headers
    headers = ['Trade', 'Equipment', 'Unit', 'WO No.', 'Description', 
               'Spare Part (Used QTY/Current Balance)']
    
    # Column widths
    col_widths = ['120px', '150px', '150px', '400px', '500px', '300px']
    
    # Sort rows by name (changebyname)
    rows = sorted(rows, key=lambda r: r[10])  # r[10] is 'changebyname' column

    all_tables_html = ""

    for name, name_group_rows in groupby(rows, key=lambda r: r[10]):
        name_group_rows = list(name_group_rows)
        
        # Sort by ownergroup, ancestor, asset, wogroup for proper grouping
        name_group_rows = sorted(name_group_rows, key=lambda r: (r[0] or '', r[1] or '', r[2] or '', r[3] or ''))
        
        # Create separate table for each person
        unique_wogroups = len(set(row[3] for row in name_group_rows if row[3]))
        table_html = f"<h3>{name or 'Unknown'} completed {unique_wogroups} work orders:</h3>"
        table_html += "<table border='1' cellspacing='0' cellpadding='4' style='border-collapse: collapse; margin-bottom: 0px; table-layout: fixed; font-family: Calibri, sans-serif;'>"
        
        # Build header row with fixed widths
        table_html += "<thead><tr>"
        for h, width in zip(headers, col_widths):
            table_html += f"<th style='background-color: #f0f0f0; width: {width};'>{h}</th><o:p></o:p>"
        table_html += "</tr></thead>"
        
        table_html += "<tbody>"
        
        # Build rowspan tracking for ownergroup, zancestor, and asset
        ownergroup_spans = {}
        zancestor_spans = {}
        asset_desc_spans = {}
        wogroup_spans = {}
        
        for i, row in enumerate(name_group_rows):
            ownergroup = row[0] or ''
            ancestor_desc = row[1] or ''
            asset_desc = row[2] or ''
            wogroup = row[3] or ''
            
            # Track ownergroup spans
            if ownergroup not in ownergroup_spans:
                ownergroup_spans[ownergroup] = []
            ownergroup_spans[ownergroup].append(i)
            
            # Track zancestor spans (need to consider ownergroup context)
            key = (ownergroup, ancestor_desc)
            if key not in zancestor_spans:
                zancestor_spans[key] = []
            zancestor_spans[key].append(i)

            # Track asset_description spans (grouped by ownergroup + ancestor_desc + asset_desc)
            key = (ownergroup, ancestor_desc, asset_desc)
            if key not in asset_desc_spans:
                asset_desc_spans[key] = []
            asset_desc_spans[key].append(i)

            # Track wogroup spans (grouped by ownergroup + ancestor_desc + asset_desc + wogroup)
            key = (ownergroup, ancestor_desc, asset_desc, wogroup)
            if key not in wogroup_spans:
                wogroup_spans[key] = []
            wogroup_spans[key].append(i)

        # Count unique wogroups for rowspan calculation
        unique_wogroups_per_ownergroup = {}
        unique_wogroups_per_ancestor = {}
        unique_wogroups_per_asset = {}
        
        for indices in ownergroup_spans.values():
            unique_wgs = set()
            for idx in indices:
                unique_wgs.add(name_group_rows[idx][3])  # wogroup
            unique_wogroups_per_ownergroup[indices[0]] = len(unique_wgs)
        
        for key, indices in zancestor_spans.items():
            unique_wgs = set()
            for idx in indices:
                unique_wgs.add(name_group_rows[idx][3])  # wogroup
            unique_wogroups_per_ancestor[indices[0]] = len(unique_wgs)
        
        for key, indices in asset_desc_spans.items():
            unique_wgs = set()
            for idx in indices:
                unique_wgs.add(name_group_rows[idx][3])  # wogroup
            unique_wogroups_per_asset[indices[0]] = len(unique_wgs)
        
        # Calculate which rows should display ownergroup cell
        ownergroup_display = {}
        for ownergroup, indices in ownergroup_spans.items():
            first_idx = indices[0]
            rowspan = unique_wogroups_per_ownergroup[first_idx]
            ownergroup_display[first_idx] = (ownergroup, rowspan)
        
        # Calculate which rows should display zancestor cell
        zancestor_display = {}
        for (ownergroup, ancestor_desc), indices in zancestor_spans.items():
            first_idx = indices[0]
            rowspan = unique_wogroups_per_ancestor[first_idx]
            zancestor_display[first_idx] = (ancestor_desc, rowspan)

        # Calculate which rows should display asset_desc cell
        asset_desc_display = {}
        for (ownergroup, ancestor_desc, asset_desc), indices in asset_desc_spans.items():
            first_idx = indices[0]
            rowspan = unique_wogroups_per_asset[first_idx]
            asset_desc_display[first_idx] = (asset_desc, rowspan)

        # Calculate which rows should display wogroup cell and aggregate descriptions and spare parts
        wogroup_display = {}
        wogroup_descriptions = {}  # Store descriptions for each wogroup
        wogroup_spare_parts = {}  # Store spare parts for each wogroup
        
        for (ownergroup, ancestor_desc, asset_desc, wogroup), indices in wogroup_spans.items():
            first_idx = indices[0]
            rowspan = len(indices)
            # Merge using formatted wogroup_content for display
            wg_value = f"{wogroup}: {name_group_rows[first_idx][4]}" if wogroup and name_group_rows[first_idx][4] else wogroup or name_group_rows[first_idx][4]
            wogroup_display[first_idx] = (wg_value, rowspan)
            
            # Aggregate descriptions for this wogroup
            descriptions_list = []
            for idx in indices:
                row = name_group_rows[idx]
                zopresult = row[5] or ''
                task_title = row[11] or ''
                zwocomments = row[12] or ''
                
                # Determine color based on zopresult
                if zopresult == "OK":
                    result_color = "#006400"  # dark green
                elif zopresult == "OK-UNWORK":
                    result_color = "gray"
                else:
                    result_color = "red"
                
                # Build description entry
                desc_entry = f"<span style='color: {result_color};'>{zopresult}: {task_title}</span>"
                if zwocomments:
                    desc_entry += f"<br><span style='color: black;'><i>{zwocomments}</i></span>"
                
                if desc_entry not in descriptions_list:
                    descriptions_list.append(desc_entry)
            
            wogroup_descriptions[first_idx] = descriptions_list
            
            # Aggregate spare parts for this wogroup
            spare_parts_list = []
            for idx in indices:
                row = name_group_rows[idx]
                itemnum = row[6] or ''
                used_qty = row[7]
                zspec = row[8] or ''
                curbal = row[9]
                z_sp_mstid = row[14]
                
                if itemnum and zspec:
                    # Format qty/balance
                    qty_balance = ""
                    if used_qty is not None and curbal is not None:
                        try:
                            qty_int = int(float(used_qty))
                            bal_int = int(float(curbal))
                            qty_balance = f"{qty_int}/{bal_int}"
                        except:
                            qty_balance = f"{used_qty}/{curbal}"
                    
                    # Add hyperlink if z_sp_mstid exists
                    if z_sp_mstid:
                        maximo_url = f"https://ems-lgensol.singlex.com/maximo/ui/?event=loadapp&value=zspmaster&uniqueid={z_sp_mstid}"
                        spare_part_entry = f"· <a href='{maximo_url}' target='_blank'>{itemnum}</a>: {zspec}({qty_balance})"
                    else:
                        spare_part_entry = f"· {itemnum}: {zspec}({qty_balance})"
                    
                    if spare_part_entry not in spare_parts_list:
                        spare_parts_list.append(spare_part_entry)
            
            wogroup_spare_parts[first_idx] = spare_parts_list

        # Map: work order identifier -> list of (doctype, weburl)
        doc_map = defaultdict(list)
        for row in rows:
            work_order_id = row[13]   # identifier
            weburl = row[-2]          # weburl
            doctype = row[-1]         # doctype
            if weburl:
                doc_map[work_order_id].append((doctype, weburl))
        
        # Process unique wogroups only (one row per wogroup)
        processed_wogroups = set()
        
        for i, row in enumerate(name_group_rows):
            ownergroup = row[0] or ''
            wogroup = row[3] or ''
            
            # Skip if we've already processed this wogroup
            wogroup_key = (row[0], row[1], row[2], row[3])
            if wogroup_key in processed_wogroups:
                continue
            processed_wogroups.add(wogroup_key)
            
            table_html += "<tr>"
            
            # Only add ownergroup cell if this is the first row for this ownergroup
            if i in ownergroup_display:
                og_value, og_rowspan = ownergroup_display[i]
                table_html += f"<td rowspan='{og_rowspan}' style='text-align: center; vertical-align: middle; width: {col_widths[0]};'>{og_value}</td>"
            
            # Only add zancestor cell if this is the first row for this zancestor (within same ownergroup)
            if i in zancestor_display:
                za_value, za_rowspan = zancestor_display[i]
                table_html += f"<td rowspan='{za_rowspan}' style='text-align: center; vertical-align: middle; width: {col_widths[1]};'>{za_value}</td>"

            # Only add asset_description cell if this is the first row for this asset_description (within same group)
            if i in asset_desc_display:
                ad_value, ad_rowspan = asset_desc_display[i]
                table_html += f"<td rowspan='{ad_rowspan}' style='text-align: center; vertical-align: middle; width: {col_widths[2]};'>{ad_value}</td>"

            # Only add wogroup cell if this is the first row for this wogroup (within same group)
            if i in wogroup_display:
                wg_value, wg_rowspan = wogroup_display[i]
                
                # Get current work order identifier
                work_order_id = name_group_rows[i][13]  # identifier
                
                # Get docs for this work order
                docs = doc_map.get(work_order_id, [])
                
                # Build doc links string
                doc_links = ""
                doc_count = defaultdict(int)
                for doctype, url in docs:
                    doc_count[doctype] += 1
                    number = doc_count[doctype]
                    suffix = f"{number}" if number > 1 else ""
                    doc_links += f"<a href='{url}' target='_blank'>{doctype}{suffix}</a><br>"
                
                table_html += f"<td style='text-align: left; vertical-align: middle; width: {col_widths[3]};'>{wg_value}<br>{doc_links}</td>"
            
            # Add descriptions column with bullet points
            if i in wogroup_descriptions:
                descriptions = wogroup_descriptions[i]
                desc_content = "<br>".join([f"· {desc}" for desc in descriptions]) if descriptions else ""
                table_html += f"<td style='text-align: left; vertical-align: middle; width: {col_widths[4]};'>{desc_content}</td>"
            
            # Add spare parts column
            if i in wogroup_spare_parts:
                spare_parts = wogroup_spare_parts[i]
                spare_content = "<br>".join(spare_parts) if spare_parts else ""
                table_html += f"<td style='text-align: left; vertical-align: middle; width: {col_widths[5]};'>{spare_content}</td>"
            
            table_html += "</tr>"
        
        table_html += "</tbody></table>"
        all_tables_html += table_html
    
    return all_tables_html

# Previous crew
crew_name = shift_utils.get_shift_letter(datetime.now(shift_utils.eastern) - timedelta(hours=12))

# Determine shift type based on 'Crew' column
shift = shift_utils.get_shift_info(crew_name)

# Sending email
outlook = win32.Dispatch('Outlook.Application')
mail = outlook.CreateItem(0)
# Set Email Subject
mail.Subject = f"EMS Work Orders Completion on {crew_name} CREW - ({shift})"

# mail.To = ';'.join([
#     '<EMAIL>',
#     '<EMAIL>',
#     # '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>'
# ])


# # Email Copy (CC)
# mail.CC = ';'.join([
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>',
#     '<EMAIL>'
# ])

# Email Body (HTML format)
mail.BodyFormat = 2  # 2 is for HTML

# Get the specific report file for the previous shift
previous_shift_time = datetime.now(shift_utils.eastern) - timedelta(hours=12)
previous_shift_start, _ = shift_utils.get_shift_start_end(previous_shift_time)
previous_shift_letter = shift_utils.get_shift_letter(previous_shift_time)

# Relative folder path (relative to your script)
folder_path = r"Daily Check KPI"

# Convert folder path to absolute
abs_folder_path = os.path.abspath(folder_path)

# Generate the specific report filename
report_filename = shift_utils.generate_report_filename(previous_shift_start, previous_shift_letter)
specific_report_path = os.path.join(abs_folder_path, report_filename)

print(f"Looking for specific report: {specific_report_path}")

# Check if the specific report exists
if not os.path.exists(specific_report_path):
    print(f"Warning: Specific report not found: {specific_report_path}")
    # Fallback to most recent PDF
    pdf_files = glob.glob(os.path.join(abs_folder_path, "*.pdf"))
    if not pdf_files:
        raise FileNotFoundError(f"No PDF files found in {abs_folder_path}")
    specific_report_path = max(pdf_files, key=os.path.getctime)
    print(f"Using most recent PDF instead: {specific_report_path}")
else:
    print(f"Found specific report: {specific_report_path}")

html_table = build_html_table(rows)

unique_wo_count = len(set(row[3] for row in rows if row[3]))

mail.HTMLBody = f"""
<div style="font-family: Calibri, sans-serif;">
    <p>Hello Ultium Facility,</p>
    <p>Here are the work orders that were completed on {crew_name}-CREW ({shift})</p>
    <p>
        Shift Start: {shift_start.strftime('%Y-%m-%d %H:%M:%S')}<br>
        Shift End: {shift_end.strftime('%Y-%m-%d %H:%M:%S')}
    </p>
    <p>Completed {unique_wo_count} total work orders:</p>
    <p></p>
    {html_table}
    <p>Thank you,<br>Joshua Jang</p>
</div>
"""

# Attach the specific report PDF
mail.Attachments.Add(specific_report_path)

mail.Display()  # Open email for review before sending
# mail.Send()

print("✅ Email draft successfully created.")