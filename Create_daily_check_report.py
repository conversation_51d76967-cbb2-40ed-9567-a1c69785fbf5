import pandas as pd
from fpdf import FPDF
import os
import re
from datetime import datetime, timedelta, timezone
import pytz
import sqlite3
import urllib3
from utils import ShiftUtils

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# File paths
DB_PATH = "data.db"
output_pdf_path = r'Daily Check KPI/most_recent_file.pdf'

def save_report(output_pdf_path, start_time, shift_letter):
    # Extract the directory path and the current filename
    directory, _ = os.path.split(output_pdf_path)

    # Use ShiftUtils to generate the file path
    shift_utils = ShiftUtils()
    new_file_path = shift_utils.get_report_file_path(directory, start_time, shift_letter)

    # Now save or rename the file (dummy print for illustration)
    print(f"Saving file to: {new_file_path}")

    return new_file_path

# Initialize ShiftUtils
shift_utils = ShiftUtils()

# Set time usage
eastern_time_zone = datetime.now(pytz.timezone('US/Eastern'))
delta_time = timedelta(hours=12)
current_shift_delta12 = eastern_time_zone - delta_time
print(f'report on shift {current_shift_delta12}')
shift_letter = shift_utils.get_shift_letter(current_shift_delta12)

print(f'\nThe previous Crew was:  {shift_letter}\n')
print(f'Current time - {delta_time} hours is: {current_shift_delta12}\n')

# Get shift start and end times
start_time, end_time = shift_utils.get_shift_start_end(current_shift_delta12)
utc_start_time = start_time.astimezone(timezone.utc)
utc_end_time = end_time.astimezone(timezone.utc)
utc_start_time = utc_start_time.isoformat()
utc_end_time = utc_end_time.isoformat()
print("Start Time:", utc_start_time)
print("End Time:", utc_end_time, "\n\n")

output_pdf_path = save_report(output_pdf_path, start_time, shift_letter)

# Mapping dictionary for 'Equipment Name' codes
equipmnet_mapping = {
    'HOT OIL CIRCULATION PUMP': 'HOT OIL PUMP',
    'ACTIVATED CARBON TOWER' : 'AC TOWER',
    'PERMANENT SAFETY EYE WASH' : 'SAFETY EYE WASH',
    'PERMANENT SAFETY SHOWER' : 'SAFETY SHOWER',
    'RO (MIXING 2ND FLOOR)' : 'RO (MIXING)',
    'DUS' : 'DUST COLLECTOR',
    'FCO' : 'FUME COLLECTOR',
    'HOT WATER BOILER' : 'HOT WATER',
    'HOT OIL BOILER' : 'HOT OIL',
    'PORTABLE SAFETY EYE WASH' : 'SAFETY EYE WASH'
}

# Load Excel files
conn = sqlite3.connect(DB_PATH)
query = """
SELECT *
FROM Daily_Check
WHERE
    zschedstart >= ?
    AND zschedstart < ?

UNION

SELECT *
FROM Daily_Check
WHERE
    zmeasuredate >= ?
    AND zmeasuredate < ?
"""

df = pd.read_sql_query(
    query,
    conn,
    params=[
        utc_start_time, utc_end_time,
        utc_start_time, utc_end_time
    ]
)

print(f"Total records to insert: {len(df)}")

# Ensure both 'Labor', 'Owning Department', and 'Measurement Result' exist in the Excel file
if 'Display Name' in df.columns and 'zownergroup' in df.columns and 'zobservation' in df.columns:
    # Count the total number of rows (for check amount) before dropping NaNs
    total_rows = len(df)

    # Drop rows with NaN in 'Labor' or 'Owning Department' or 'Measurement Result'
    df = df[['Display Name', 'zownergroup', 'zobservation', 'Equipment Name']].dropna()

    # Group by 'Owning Department' and concatenate the unique Labors
    grouped_data = df.groupby('zownergroup')['Display Name'].apply(lambda x: ', '.join(x.unique())).reset_index()

    # Calculate summary
    result_counts = df['zobservation'].value_counts()
    count_ok = result_counts.get('OK', 0)
    count_ok_unc = result_counts.get('OK-UNCHECK', 0)
    count_ng_adj = result_counts.get('NG-ADJUST', 0)
    count_ng_wo = result_counts.get('NG-WO', 0)
    amount_checked = df['zobservation'].notna().sum()
    completion_percent = (amount_checked / total_rows) * 100 if total_rows > 0 else 0

    # Create PDF report
    pdf = FPDF()
    pdf.add_page()
    pdf.set_font('Arial', 'B', 12)

    shift_info = shift_utils.get_shift_info(shift_letter)

    # Title
    pdf.cell(200, 6, f'{shift_letter}-Crew Check Report', ln=True, align='C')
    pdf.set_font('Arial', 'B', 10)
    pdf.cell(200, 4, f'{start_time.date()} {shift_info} Shift', ln=True, align='C')

    # Subheader for the report
    pdf.ln(-3)
    pdf.set_font('Arial', 'B', 8)
    pdf.cell(200, 10, 'Crew List', ln=True)

    # List the concatenated Labors by department
    pdf.set_font('Arial', '', 8)
    for index, row in grouped_data.iterrows():
        pdf.ln(-4)
        department = row['zownergroup']
        inspectors = row['Display Name']
        pdf.cell(200, 7, f'{department}: {inspectors}', ln=True)

    # Set border color to light gray (RGB: 211, 211, 211)
    pdf.set_draw_color(211, 211, 211)

    # Summary Section
    pdf.set_font('Arial', 'B', 9)
    pdf.cell(200, 10, 'Summary', ln=True, align='C')

    # Create 'Summary' table with centered values
    pdf.ln(-2)
    pdf.set_font('Arial', 'B', 8)
    pdf.cell(30, 6, 'OVERALL', 1, align='C')
    pdf.cell(10, 6, 'OK', 1, align='C')
    pdf.cell(25, 6, 'OK-UNCHECK', 1, align='C')
    pdf.cell(25, 6, 'NG-ADJUST', 1, align='C')
    pdf.cell(20, 6, 'NG-WO', 1, align='C')
    pdf.cell(30, 6, 'Amount Checked', 1, align='C')
    pdf.cell(25, 6, 'Check Amount', 1, align='C')
    pdf.cell(25, 6, 'Completion %', 1, align='C')
    pdf.ln()

    # Fill in the summary table data
    pdf.set_font('Arial', '', 8)
    pdf.cell(30, 6, f'{shift_letter}-Crew', 1, align='C')
    pdf.cell(10, 6, str(count_ok), 1, align='C')
    pdf.cell(25, 6, str(count_ok_unc), 1, align='C')
    pdf.cell(25, 6, str(count_ng_adj), 1, align='C')
    pdf.cell(20, 6, str(count_ng_wo), 1, align='C')
    pdf.cell(30, 6, str(amount_checked), 1, align='C')
    pdf.cell(25, 6, str(total_rows), 1, align='C')
    pdf.cell(25, 6, f'{completion_percent:.2f}%', 1, align='C')

    # Load Excel files
    conn = sqlite3.connect(DB_PATH)
    df = pd.read_sql_query("SELECT * FROM Daily_Check", conn)

    # Add 'By Trade' section
    pdf.ln(5)
    pdf.set_font('Arial', 'B', 9)
    pdf.cell(200, 10, 'By Trade', ln=True, align='C')

    # Group by 'Owning Department' for 'By Trade' table
    grouped_trade = df.groupby('zownergroup').agg(
        OK=('zobservation', lambda x: (x == 'OK').sum()),
        OK_UNCHECK=('zobservation', lambda x: (x == 'OK-UNCHECK').sum()),
        NG_ADJUST=('zobservation', lambda x: (x == 'NG-ADJUST').sum()),
        NG_WO=('zobservation', lambda x: (x == 'NG-WO').sum()),
        Amount_Checked=('zobservation', lambda x: x.notna().sum()),
        Check_Amount=('zobservation', 'size')
    ).reset_index()

    # Add Completion % to the grouped data
    grouped_trade['Completion %'] = (grouped_trade['Amount_Checked'] / grouped_trade['Check_Amount']) * 100

    # Create 'By Trade' table with centered values
    pdf.ln(-2)
    pdf.set_font('Arial', 'B', 8)
    pdf.cell(30, 6, 'Trade', 1, align='C')
    pdf.cell(10, 6, 'OK', 1, align='C')
    pdf.cell(25, 6, 'OK-UNCHECK', 1, align='C')
    pdf.cell(25, 6, 'NG-ADJUST', 1, align='C')
    pdf.cell(20, 6, 'NG-WO', 1, align='C')
    pdf.cell(30, 6, 'Amount Checked', 1, align='C')
    pdf.cell(25, 6, 'Check Amount', 1, align='C')
    pdf.cell(25, 6, 'Completion %', 1, align='C')
    pdf.ln()

    # Fill in the 'By Trade' table data
    pdf.set_font('Arial', '', 8)
    for index, row in grouped_trade.iterrows():
        pdf.cell(30, 6, row['zownergroup'], 1, align='C')
        pdf.cell(10, 6, str(row['OK']), 1, align='C')
        pdf.cell(25, 6, str(row['OK_UNCHECK']), 1, align='C')
        pdf.cell(25, 6, str(row['NG_ADJUST']), 1, align='C')
        pdf.cell(20, 6, str(row['NG_WO']), 1, align='C')
        pdf.cell(30, 6, str(row['Amount_Checked']), 1, align='C')
        pdf.cell(25, 6, str(row['Check_Amount']), 1, align='C')
        pdf.cell(25, 6, f'{row["Completion %"]:.2f}%', 1, align='C')
        pdf.ln()

    # Group by 'Owning Department' and 'Equipment Name'
    grouped_by_department = df.groupby('zownergroup')

    # Add 'By Equipment Name' section
    for department, group in grouped_by_department:
        pdf.ln(0)
        pdf.set_font('Arial', 'B', 9)
        pdf.cell(200, 10, f'{department}', ln=True, align='C')

        # Group by 'Equipment Name' for current department
        grouped_equipment = group.groupby('Ancestor Name').agg(
            OK=('zobservation', lambda x: (x == 'OK').sum()),
            OK_UNCHECK=('zobservation', lambda x: (x == 'OK-UNCHECK').sum()),
            NG_ADJUST=('zobservation', lambda x: (x == 'NG-ADJUST').sum()),
            NG_WO=('zobservation', lambda x: (x == 'NG-WO').sum()),
            Amount_Checked=('zobservation', lambda x: x.notna().sum()),
            Check_Amount=('zobservation', 'size')
        ).reset_index()

        # Add Completion % to the grouped data
        grouped_equipment['Completion %'] = (grouped_equipment['Amount_Checked'] / grouped_equipment['Check_Amount']) * 100

        # Create 'By Equipment Name' table with centered values
        pdf.ln(-2)
        pdf.set_font('Arial', 'B', 8)
        pdf.cell(30, 6, 'Unit', 1, align='C')
        pdf.cell(10, 6, 'OK', 1, align='C')
        pdf.cell(25, 6, 'OK-UNCHECK', 1, align='C')
        pdf.cell(25, 6, 'NG-ADJUST', 1, align='C')
        pdf.cell(20, 6, 'NG-WO', 1, align='C')
        pdf.cell(30, 6, 'Amount Checked', 1, align='C')
        pdf.cell(25, 6, 'Check Amount', 1, align='C')
        pdf.cell(25, 6, 'Completion %', 1, align='C')
        pdf.ln()

        # Fill in the 'By Equipment Name' table data
        pdf.set_font('Arial', '', 8)
        for index, row in grouped_equipment.iterrows():
            pdf.cell(30, 6, row['Ancestor Name'], 1, align='C')
            pdf.cell(10, 6, str(row['OK']), 1, align='C')
            pdf.cell(25, 6, str(row['OK_UNCHECK']), 1, align='C')
            pdf.cell(25, 6, str(row['NG_ADJUST']), 1, align='C')
            pdf.cell(20, 6, str(row['NG_WO']), 1, align='C')
            pdf.cell(30, 6, str(row['Amount_Checked']), 1, align='C')
            pdf.cell(25, 6, str(row['Check_Amount']), 1, align='C')
            pdf.cell(25, 6, f'{row["Completion %"]:.2f}%', 1, align='C')
            pdf.ln()

    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(output_pdf_path), exist_ok=True)

    # Save the PDF to the specified path
    pdf.output(output_pdf_path)

    print(f"PDF report saved to {output_pdf_path}")

else:
    raise ValueError("'Display Name', 'zownergroup', or 'zobservation' column not found in the Excel file.")